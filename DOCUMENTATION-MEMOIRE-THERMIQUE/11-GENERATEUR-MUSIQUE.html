<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎵 Générateur Musical IA - Louna QI 235</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .generation-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 10px;
            background: rgba(255,255,255,0.2);
            color: white;
            font-size: 16px;
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: rgba(255,255,255,0.7);
        }

        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            border-radius: 15px;
            color: white;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .results-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .music-player {
            background: rgba(0,0,0,0.3);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .waveform {
            height: 100px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .waveform-bars {
            display: flex;
            align-items: end;
            height: 80px;
            gap: 2px;
        }

        .bar {
            width: 3px;
            background: linear-gradient(to top, #ff6b6b, #ee5a24);
            border-radius: 2px;
            animation: wave 2s ease-in-out infinite;
        }

        @keyframes wave {
            0%, 100% { height: 20%; }
            50% { height: 100%; }
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
        }

        .control-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.2);
            border-radius: 3px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #ee5a24);
            width: 0%;
            transition: width 0.3s ease;
        }

        .music-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .info-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .info-item .label {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .info-item .value {
            font-size: 18px;
            font-weight: 600;
        }

        .generation-history {
            margin-top: 30px;
        }

        .history-item {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .history-info h4 {
            margin-bottom: 5px;
        }

        .history-info p {
            opacity: 0.8;
            font-size: 14px;
        }

        .download-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ready { background: #2ecc71; }
        .status-generating { background: #f39c12; animation: pulse 1s infinite; }
        .status-error { background: #e74c3c; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .music-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 Générateur Musical IA</h1>
            <p>Louna QI 235 - Composition Musicale Avancée</p>
        </div>

        <div class="nav-buttons">
            <a href="/" class="nav-btn"><i class="fas fa-home"></i> Accueil</a>
            <a href="/generation-center.html" class="nav-btn"><i class="fas fa-magic"></i> Centre Génération</a>
            <a href="/chat-intelligent.html" class="nav-btn"><i class="fas fa-comments"></i> Chat IA</a>
        </div>

        <div class="main-content">
            <div class="generation-panel">
                <h2><i class="fas fa-music"></i> Créer une Musique</h2>
                
                <form id="musicForm">
                    <div class="form-group">
                        <label for="prompt">Description de la musique :</label>
                        <textarea id="prompt" placeholder="Ex: Une mélodie relaxante au piano avec des cordes, style ambient..." rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="genre">Genre Musical :</label>
                        <select id="genre">
                            <option value="ambient">Ambient</option>
                            <option value="classical">Classique</option>
                            <option value="electronic">Électronique</option>
                            <option value="jazz">Jazz</option>
                            <option value="rock">Rock</option>
                            <option value="pop">Pop</option>
                            <option value="cinematic">Cinématique</option>
                            <option value="lofi">Lo-Fi</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="duration">Durée (secondes) :</label>
                        <input type="number" id="duration" value="60" min="10" max="300">
                    </div>

                    <div class="form-group">
                        <label for="tempo">Tempo (BPM) :</label>
                        <input type="number" id="tempo" value="120" min="60" max="200">
                    </div>

                    <div class="form-group">
                        <label for="key">Tonalité :</label>
                        <select id="key">
                            <option value="C">Do Majeur</option>
                            <option value="Am">La Mineur</option>
                            <option value="G">Sol Majeur</option>
                            <option value="Em">Mi Mineur</option>
                            <option value="D">Ré Majeur</option>
                            <option value="Bm">Si Mineur</option>
                        </select>
                    </div>

                    <button type="submit" class="generate-btn" id="generateBtn">
                        <i class="fas fa-magic"></i> Générer la Musique
                    </button>
                </form>
            </div>

            <div class="results-panel">
                <h2><i class="fas fa-headphones"></i> Lecteur Musical</h2>
                
                <div class="music-player" id="musicPlayer">
                    <div class="status-indicator status-ready" id="statusIndicator"></div>
                    <span id="statusText">Prêt à générer</span>
                    
                    <div class="waveform" id="waveform">
                        <div class="waveform-bars" id="waveformBars">
                            <!-- Barres générées dynamiquement -->
                        </div>
                    </div>

                    <div class="controls">
                        <button class="control-btn" id="playBtn"><i class="fas fa-play"></i></button>
                        <button class="control-btn" id="pauseBtn"><i class="fas fa-pause"></i></button>
                        <button class="control-btn" id="stopBtn"><i class="fas fa-stop"></i></button>
                        <button class="control-btn" id="downloadBtn"><i class="fas fa-download"></i></button>
                    </div>

                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </div>

                <div class="music-info" id="musicInfo">
                    <div class="info-item">
                        <div class="label">Durée</div>
                        <div class="value" id="durationInfo">--:--</div>
                    </div>
                    <div class="info-item">
                        <div class="label">Genre</div>
                        <div class="value" id="genreInfo">--</div>
                    </div>
                    <div class="info-item">
                        <div class="label">Tempo</div>
                        <div class="value" id="tempoInfo">-- BPM</div>
                    </div>
                    <div class="info-item">
                        <div class="label">Tonalité</div>
                        <div class="value" id="keyInfo">--</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="generation-history">
            <h2><i class="fas fa-history"></i> Historique des Générations</h2>
            <div id="historyContainer">
                <!-- Historique généré dynamiquement -->
            </div>
        </div>
    </div>

    <script>
        let currentMusic = null;
        let isPlaying = false;
        let currentTime = 0;
        let duration = 0;

        // Initialiser les barres de forme d'onde
        function initWaveform() {
            const waveformBars = document.getElementById('waveformBars');
            waveformBars.innerHTML = '';
            
            for (let i = 0; i < 50; i++) {
                const bar = document.createElement('div');
                bar.className = 'bar';
                bar.style.height = Math.random() * 80 + 20 + '%';
                bar.style.animationDelay = Math.random() * 2 + 's';
                waveformBars.appendChild(bar);
            }
        }

        // Mettre à jour le statut
        function updateStatus(status, text) {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            
            indicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }

        // Générer de la musique
        async function generateMusic(formData) {
            updateStatus('generating', 'Génération en cours...');
            
            try {
                const response = await fetch('/api/generation/music', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                
                if (result.success) {
                    currentMusic = result.music;
                    updateMusicInfo(currentMusic);
                    updateStatus('ready', 'Musique générée avec succès');
                    addToHistory(currentMusic);
                    
                    // Simuler le chargement progressif
                    simulateProgress();
                } else {
                    updateStatus('error', 'Erreur de génération');
                    console.error('Erreur:', result.error);
                }
            } catch (error) {
                updateStatus('error', 'Erreur de connexion');
                console.error('Erreur:', error);
            }
        }

        // Mettre à jour les informations de la musique
        function updateMusicInfo(music) {
            document.getElementById('durationInfo').textContent = formatTime(music.duration);
            document.getElementById('genreInfo').textContent = music.genre;
            document.getElementById('tempoInfo').textContent = music.tempo + ' BPM';
            document.getElementById('keyInfo').textContent = music.key || 'C';
        }

        // Formater le temps
        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        }

        // Simuler la progression
        function simulateProgress() {
            const progressFill = document.getElementById('progressFill');
            let progress = 0;
            
            const interval = setInterval(() => {
                progress += 2;
                progressFill.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                }
            }, 100);
        }

        // Ajouter à l'historique
        function addToHistory(music) {
            const historyContainer = document.getElementById('historyContainer');
            
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            historyItem.innerHTML = `
                <div class="history-info">
                    <h4>${music.prompt.substring(0, 50)}...</h4>
                    <p>${music.genre} • ${music.duration}s • ${music.tempo} BPM</p>
                </div>
                <button class="download-btn" onclick="downloadMusic('${music.id}')">
                    <i class="fas fa-download"></i> Télécharger
                </button>
            `;
            
            historyContainer.insertBefore(historyItem, historyContainer.firstChild);
        }

        // Télécharger la musique
        function downloadMusic(musicId) {
            // Simuler le téléchargement
            const link = document.createElement('a');
            link.href = `/generated/music/${musicId}.wav`;
            link.download = `louna_music_${musicId}.wav`;
            link.click();
        }

        // Gestionnaires d'événements
        document.getElementById('musicForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                prompt: document.getElementById('prompt').value,
                genre: document.getElementById('genre').value,
                duration: parseInt(document.getElementById('duration').value),
                tempo: parseInt(document.getElementById('tempo').value),
                key: document.getElementById('key').value
            };
            
            await generateMusic(formData);
        });

        document.getElementById('playBtn').addEventListener('click', () => {
            if (currentMusic) {
                isPlaying = true;
                updateStatus('generating', 'Lecture en cours...');
                simulateProgress();
            }
        });

        document.getElementById('pauseBtn').addEventListener('click', () => {
            isPlaying = false;
            updateStatus('ready', 'Lecture en pause');
        });

        document.getElementById('stopBtn').addEventListener('click', () => {
            isPlaying = false;
            currentTime = 0;
            document.getElementById('progressFill').style.width = '0%';
            updateStatus('ready', 'Lecture arrêtée');
        });

        document.getElementById('downloadBtn').addEventListener('click', () => {
            if (currentMusic) {
                downloadMusic(currentMusic.id);
            }
        });

        // Initialisation
        initWaveform();
        updateStatus('ready', 'Prêt à générer');
    </script>
</body>
</html>
