<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accélérateurs Kyber - Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px 0;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            color: #ff6b35;
        }

        .header-buttons {
            display: flex;
            gap: 10px;
        }

        .header-btn {
            padding: 8px 16px;
            border-radius: 20px;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-accueil {
            background: #e91e63;
            color: white;
        }

        .btn-chat {
            background: #9c27b0;
            color: white;
        }

        .btn-memoire {
            background: #3f51b5;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 10px;
            color: #ff6b35;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.8;
        }

        .accelerators-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .accelerator-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .accelerator-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .accelerator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .accelerator-name {
            font-size: 16px;
            font-weight: 600;
        }

        .accelerator-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #4caf50;
            color: white;
        }

        .status-veille {
            background: #ff9800;
            color: white;
        }

        .accelerator-metrics {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .metric-label {
            font-size: 12px;
            opacity: 0.7;
        }

        .performance-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .performance-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b35, #f7931e);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .performance-text {
            font-size: 12px;
            text-align: center;
            opacity: 0.8;
        }

        .controls-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .controls-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .controls-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .control-btn {
            padding: 12px 24px;
            border-radius: 25px;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-boost {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
        }

        .btn-optimiser {
            background: linear-gradient(135deg, #00bcd4, #00acc1);
            color: white;
        }

        .btn-reinitialiser {
            background: linear-gradient(135deg, #f44336, #e53935);
            color: white;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00ff88, #00ccff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1em;
            opacity: 0.8;
        }

        .accelerators-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .accelerator-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .accelerator-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 255, 136, 0.3);
        }

        .accelerator-card.active {
            border-color: #00ff88;
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
        }

        .accelerator-card.installed {
            border-color: #00ccff;
        }

        .accelerator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .accelerator-name {
            font-size: 1.3em;
            font-weight: bold;
        }

        .accelerator-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-active {
            background: #00ff88;
            color: #000;
        }

        .status-installed {
            background: #00ccff;
            color: #000;
        }

        .status-available {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
        }

        .accelerator-info {
            margin-bottom: 15px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .accelerator-description {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .accelerator-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            flex: 1;
        }

        .btn-install {
            background: #00ccff;
            color: #000;
        }

        .btn-activate {
            background: #00ff88;
            color: #000;
        }

        .btn-deactivate {
            background: #ff4444;
            color: #fff;
        }

        .btn:hover {
            transform: scale(1.05);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #00ff88, #00ccff);
            border: none;
            color: #000;
            font-size: 1.5em;
            cursor: pointer;
            box-shadow: 0 5px 20px rgba(0, 255, 136, 0.3);
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            transform: scale(1.1) rotate(180deg);
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
        }

        .error {
            background: rgba(255, 68, 68, 0.2);
            border: 1px solid #ff4444;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Tableau de Bord des Accélérateurs</h1>
            <p>Gestion automatique des accélérateurs Louna QI 225</p>
        </div>

        <div id="stats" class="stats-grid">
            <!-- Les statistiques seront chargées ici -->
        </div>

        <div id="accelerators" class="accelerators-grid">
            <!-- Les accélérateurs seront chargés ici -->
        </div>

        <div id="loading" class="loading pulse">
            🔄 Chargement des accélérateurs...
        </div>

        <div id="error" class="error" style="display: none;">
            ❌ Erreur de chargement des données
        </div>
    </div>

    <button class="refresh-btn" onclick="loadData()" title="Actualiser">
        🔄
    </button>

    <script>
        let acceleratorsData = null;

        async function loadData() {
            try {
                document.getElementById('loading').style.display = 'block';
                document.getElementById('error').style.display = 'none';

                const response = await fetch('/api/specialized-accelerators/status');
                const data = await response.json();

                if (data.success) {
                    acceleratorsData = data;
                    displayStats(data);
                    displayAccelerators(data.available);
                } else {
                    throw new Error(data.error || 'Erreur inconnue');
                }
            } catch (error) {
                console.error('Erreur:', error);
                document.getElementById('error').style.display = 'block';
                document.getElementById('error').textContent = `❌ ${error.message}`;
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        function displayStats(data) {
            const statsHtml = `
                <div class="stat-card">
                    <div class="stat-number" style="color: #00ff88;">${data.totalAccelerators}</div>
                    <div class="stat-label">Accélérateurs Disponibles</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #00ccff;">${data.installedCount}</div>
                    <div class="stat-label">Installés</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #ffaa00;">${data.activeCount}</div>
                    <div class="stat-label">Actifs</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: ${data.thermalMemoryConnected ? '#00ff88' : '#ff4444'};">
                        ${data.thermalMemoryConnected ? '✅' : '❌'}
                    </div>
                    <div class="stat-label">Mémoire Thermique</div>
                </div>
            `;
            document.getElementById('stats').innerHTML = statsHtml;
        }

        function displayAccelerators(accelerators) {
            const acceleratorsHtml = accelerators.map(acc => {
                const statusClass = acc.isActive ? 'active' : (acc.isInstalled ? 'installed' : '');
                const statusText = acc.isActive ? 'ACTIF' : (acc.isInstalled ? 'INSTALLÉ' : 'DISPONIBLE');
                const statusBadgeClass = acc.isActive ? 'status-active' : (acc.isInstalled ? 'status-installed' : 'status-available');

                return `
                    <div class="accelerator-card ${statusClass}">
                        <div class="accelerator-header">
                            <div class="accelerator-name">${acc.name}</div>
                            <div class="accelerator-status ${statusBadgeClass}">${statusText}</div>
                        </div>
                        <div class="accelerator-info">
                            <div class="info-row">
                                <span>Type:</span>
                                <span>${acc.type}</span>
                            </div>
                            <div class="info-row">
                                <span>Boost:</span>
                                <span>${acc.boost}x</span>
                            </div>
                            <div class="info-row">
                                <span>Durée:</span>
                                <span>${Math.round(acc.duration / 60000)} min</span>
                            </div>
                        </div>
                        <div class="accelerator-description">
                            ${acc.description}
                        </div>
                        <div class="accelerator-actions">
                            ${getActionButtons(acc)}
                        </div>
                    </div>
                `;
            }).join('');

            document.getElementById('accelerators').innerHTML = acceleratorsHtml;
        }

        function getActionButtons(acc) {
            if (acc.isActive) {
                return `<button class="btn btn-deactivate" onclick="deactivateAccelerator('${acc.id}')">Désactiver</button>`;
            } else if (acc.isInstalled) {
                return `<button class="btn btn-activate" onclick="activateAccelerator('${acc.id}')">Activer</button>`;
            } else {
                return `<button class="btn btn-install" onclick="installAccelerator('${acc.id}')">Installer</button>`;
            }
        }

        async function installAccelerator(id) {
            try {
                const response = await fetch(`/api/specialized-accelerators/install/${id}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ reason: 'Installation via dashboard' })
                });
                const result = await response.json();

                if (result.success) {
                    await loadData(); // Recharger les données
                } else {
                    alert(`Erreur: ${result.message}`);
                }
            } catch (error) {
                alert(`Erreur: ${error.message}`);
            }
        }

        async function activateAccelerator(id) {
            try {
                const response = await fetch(`/api/specialized-accelerators/activate/${id}`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    await loadData(); // Recharger les données
                } else {
                    alert(`Erreur: ${result.message}`);
                }
            } catch (error) {
                alert(`Erreur: ${error.message}`);
            }
        }

        async function deactivateAccelerator(id) {
            try {
                const response = await fetch(`/api/specialized-accelerators/deactivate/${id}`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    await loadData(); // Recharger les données
                } else {
                    alert(`Erreur: ${result.message}`);
                }
            } catch (error) {
                alert(`Erreur: ${error.message}`);
            }
        }

        // Charger les données au démarrage
        loadData();

        // Actualiser automatiquement toutes les 10 secondes
        setInterval(loadData, 10000);
    </script>
</body>
</html>
