<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI - Agent Évolué</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            text-align: center;
            padding: 20px;
            background: rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid #ff6b9d;
        }

        .header h1 {
            font-size: 2.5em;
            background: linear-gradient(45deg, #ff6b9d, #c44569, #f8b500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .nav-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .nav-btn.accueil { background: linear-gradient(45deg, #4CAF50, #45a049); }
        .nav-btn.principal { background: linear-gradient(45deg, #9b59b6, #8e44ad); }
        .nav-btn.terminal { background: linear-gradient(45deg, #3498db, #2980b9); }
        .nav-btn.shared { background: linear-gradient(45deg, #f39c12, #e67e22); }
        .nav-btn.surveillance { background: linear-gradient(45deg, #e74c3c, #c0392b); }
        .nav-btn.changements { background: linear-gradient(45deg, #1abc9c, #16a085); }
        .nav-btn.memoire { background: linear-gradient(45deg, #e91e63, #ad1457); }

        .nav-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.3);
        }

        .main-container {
            padding: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2em;
            margin: 40px 0 30px 0;
            background: linear-gradient(45deg, #ff6b9d, #c44569);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .apps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
        }

        .app-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .app-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            border-color: #ff6b9d;
        }

        .app-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .app-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #ff6b9d;
        }

        .app-description {
            font-size: 0.9em;
            opacity: 0.8;
            line-height: 1.4;
        }

        /* Couleurs spécifiques pour chaque type d'app */
        .app-card.chat { border-left: 5px solid #ff6b9d; }
        .app-card.generation { border-left: 5px solid #f39c12; }
        .app-card.security { border-left: 5px solid #e74c3c; }
        .app-card.system { border-left: 5px solid #3498db; }
        .app-card.advanced { border-left: 5px solid #9b59b6; }
        .app-card.monitoring { border-left: 5px solid #1abc9c; }

        .status-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            text-align: center;
            font-size: 0.9em;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-active { background: #4CAF50; }
        .status-warning { background: #f39c12; }
        .status-error { background: #e74c3c; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ff6b9d;
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
        }
    </style>
</head>
<body>
    <!-- Particules flottantes -->
    <div class="floating-particles" id="particles"></div>

    <!-- En-tête -->
    <div class="header">
        <h1>🧠 AGENT ÉVOLUÉ</h1>
        <p>Intelligence Artificielle Avancée - QI 235 - Système Neuronal Adaptatif</p>

        <div class="nav-buttons">
            <a href="#" class="nav-btn accueil">🏠 Accueil</a>
            <a href="#principal" class="nav-btn principal">🎯 Applications</a>
            <a href="/advanced" class="nav-btn principal">🚀 Avancées</a>
            <a href="#" class="nav-btn terminal">💻 Terminal</a>
            <a href="#" class="nav-btn shared">📤 Partagé</a>
            <a href="#" class="nav-btn surveillance">👁️ Surveillance</a>
            <a href="#" class="nav-btn changements">🔄 Changements</a>
            <a href="#" class="nav-btn memoire">🧠 Mémoire</a>
        </div>
    </div>

    <div class="main-container">
        <!-- Applications Principales -->
        <div id="principal">
            <h2 class="section-title">🎯 Applications Principales</h2>
            <div class="apps-grid">
                <div class="app-card chat" onclick="openApp('chat-intelligent')">
                    <div class="app-icon">🧠</div>
                    <div class="app-title">Chat Intelligent Avancé</div>
                    <div class="app-description">Conversations avec QI 235</div>
                </div>

                <div class="app-card generation" onclick="openApp('generation-ia')">
                    <div class="app-icon">✏️</div>
                    <div class="app-title">Centre de Génération IA</div>
                    <div class="app-description">Images, Vidéos, Musique, Textes</div>
                </div>

                <div class="app-card generation" onclick="openApp('generation-images')">
                    <div class="app-icon">🖼️</div>
                    <div class="app-title">Génération d'Images</div>
                    <div class="app-description">Création d'images IA</div>
                </div>

                <div class="app-card generation" onclick="openApp('generation-video')">
                    <div class="app-icon">🎬</div>
                    <div class="app-title">Génération Vidéo LTX</div>
                    <div class="app-description">Vidéos haute qualité</div>
                </div>

                <div class="app-card security" onclick="openApp('security-center')">
                    <div class="app-icon">🛡️</div>
                    <div class="app-title">Centre de Sécurité Avancée</div>
                    <div class="app-description">Protection complète</div>
                </div>

                <div class="app-card generation" onclick="openApp('music-generator')">
                    <div class="app-icon">🎵</div>
                    <div class="app-title">Générateur Musical IA</div>
                    <div class="app-description">Compositions musicales</div>
                </div>

                <div class="app-card system" onclick="openApp('studio-medias')">
                    <div class="app-icon">📤</div>
                    <div class="app-title">Studio Médias</div>
                    <div class="app-description">Upload et modification d'images</div>
                </div>

                <div class="app-card security" onclick="openApp('emergency-control')">
                    <div class="app-icon">🚨</div>
                    <div class="app-title">Contrôle d'Urgence</div>
                    <div class="app-description">Arrêt d'urgence système</div>
                </div>

                <div class="app-card system" onclick="openApp('backup-system')">
                    <div class="app-icon">💾</div>
                    <div class="app-title">Sauvegarde Système</div>
                    <div class="app-description">Backup automatique</div>
                </div>

                <div class="app-card system" onclick="openApp('presentation')">
                    <div class="app-icon">📋</div>
                    <div class="app-title">Présentation Complète</div>
                    <div class="app-description">Guide complet Louna</div>
                </div>

                <div class="app-card system" onclick="openApp('web-search')">
                    <div class="app-icon">🔍</div>
                    <div class="app-title">Recherche Web</div>
                    <div class="app-description">Recherche intelligente</div>
                </div>

                <div class="app-card system" onclick="openApp('web-browser')">
                    <div class="app-icon">🌐</div>
                    <div class="app-title">Navigateur Web Intégré</div>
                    <div class="app-description">Navigation web</div>
                </div>

                <div class="app-card advanced" onclick="openApp('facial-recognition')">
                    <div class="app-icon">👤</div>
                    <div class="app-title">Reconnaissance Faciale</div>
                    <div class="app-description">Analyse faciale IA</div>
                </div>

                <div class="app-card system" onclick="openApp('voice-system')">
                    <div class="app-icon">🎤</div>
                    <div class="app-title">Système Vocal Féminin</div>
                    <div class="app-description">Voix de personnalité</div>
                </div>

                <div class="app-card system" onclick="openApp('youtube-learning')">
                    <div class="app-icon">🎓</div>
                    <div class="app-title">Apprentissage Vocal YouTube</div>
                    <div class="app-description">Via vidéos éducatives</div>
                </div>

                <div class="app-card system" onclick="openApp('camera-phone')">
                    <div class="app-icon">📱</div>
                    <div class="app-title">Caméra/Micro Téléphone</div>
                    <div class="app-description">Connexion Wi-Fi</div>
                </div>

                <div class="app-card system" onclick="openApp('code-editor')">
                    <div class="app-icon">💻</div>
                    <div class="app-title">Éditeur Code</div>
                    <div class="app-description">IDE professionnel</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Barre de statut -->
    <div class="status-bar">
        <span class="status-indicator status-active"></span>Sauvegarde automatique active
        <span class="status-indicator status-active"></span>Mémoire thermique en formation
        <span class="status-indicator status-warning"></span>Accélérateurs Kyber chargés
        <span class="status-indicator status-active"></span>Système de monitoring initialisé
        <span class="status-indicator status-active"></span>Protection d'urgence active
    </div>

    <script>
        // Création des particules flottantes
        function createParticles() {
            const container = document.getElementById('particles');
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                container.appendChild(particle);
            }
        }

        // Fonction pour ouvrir les applications
        function openApp(appName) {
            console.log('Ouverture de l\'application:', appName);

            // Mapping des applications vers leurs fichiers
            const appRoutes = {
                'chat-intelligent': '/chat',
                'generation-ia': '/generation',
                'generation-images': '/images',
                'generation-video': '/video',
                'security-center': '/security',
                'music-generator': '/music',
                'studio-medias': '/studio',
                'emergency-control': '/emergency',
                'backup-system': '/backup',
                'presentation': '/presentation',
                'web-search': '/search',
                'web-browser': '/browser',
                'facial-recognition': '/facial',
                'voice-system': '/voice',
                'youtube-learning': '/youtube',
                'camera-phone': '/camera',
                'code-editor': '/editor'
            };

            if (appRoutes[appName]) {
                window.location.href = appRoutes[appName];
            } else {
                alert('Application en cours de développement: ' + appName);
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();

            // Animation d'entrée pour les cartes
            const cards = document.querySelectorAll('.app-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
