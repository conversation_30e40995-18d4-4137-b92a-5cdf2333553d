<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Générateur 3D IA - Louna QI 235</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .generation-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            height: fit-content;
        }

        .preview-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 10px;
            background: rgba(255,255,255,0.2);
            color: white;
            font-size: 16px;
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: rgba(255,255,255,0.7);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border: none;
            border-radius: 15px;
            color: white;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .model-viewer {
            background: rgba(0,0,0,0.3);
            border-radius: 15px;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .model-placeholder {
            text-align: center;
            opacity: 0.7;
        }

        .model-placeholder i {
            font-size: 4em;
            margin-bottom: 15px;
            display: block;
        }

        .model-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .control-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 10px;
            padding: 10px 15px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .model-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .info-item .label {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .info-item .value {
            font-size: 18px;
            font-weight: 600;
        }

        .progress-container {
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-size: 14px;
            opacity: 0.8;
        }

        .model-gallery {
            margin-top: 30px;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .gallery-item {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .gallery-item:hover {
            transform: translateY(-5px);
            background: rgba(255,255,255,0.2);
        }

        .gallery-preview {
            width: 100%;
            height: 120px;
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
        }

        .gallery-preview i {
            font-size: 2em;
            opacity: 0.7;
        }

        .gallery-info h4 {
            margin-bottom: 5px;
            font-size: 14px;
        }

        .gallery-info p {
            font-size: 12px;
            opacity: 0.8;
        }

        .download-options {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .download-btn {
            flex: 1;
            background: rgba(255,255,255,0.2);
            border: none;
            padding: 12px;
            border-radius: 10px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ready { background: #2ecc71; }
        .status-generating { background: #f39c12; animation: pulse 1s infinite; }
        .status-error { background: #e74c3c; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .rotating-cube {
            width: 100px;
            height: 100px;
            position: relative;
            transform-style: preserve-3d;
            animation: rotate 4s linear infinite;
        }

        .cube-face {
            position: absolute;
            width: 100px;
            height: 100px;
            background: rgba(52, 152, 219, 0.8);
            border: 2px solid rgba(255,255,255,0.3);
        }

        .cube-face.front { transform: rotateY(0deg) translateZ(50px); }
        .cube-face.back { transform: rotateY(180deg) translateZ(50px); }
        .cube-face.right { transform: rotateY(90deg) translateZ(50px); }
        .cube-face.left { transform: rotateY(-90deg) translateZ(50px); }
        .cube-face.top { transform: rotateX(90deg) translateZ(50px); }
        .cube-face.bottom { transform: rotateX(-90deg) translateZ(50px); }

        @keyframes rotate {
            0% { transform: rotateX(0deg) rotateY(0deg); }
            100% { transform: rotateX(360deg) rotateY(360deg); }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .model-info {
                grid-template-columns: 1fr;
            }
            
            .gallery-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Générateur 3D IA</h1>
            <p>Louna QI 235 - Modélisation 3D Avancée</p>
        </div>

        <div class="nav-buttons">
            <a href="/" class="nav-btn"><i class="fas fa-home"></i> Accueil</a>
            <a href="/generation-center.html" class="nav-btn"><i class="fas fa-magic"></i> Centre Génération</a>
            <a href="/music-generator.html" class="nav-btn"><i class="fas fa-music"></i> Musique IA</a>
        </div>

        <div class="main-content">
            <div class="generation-panel">
                <h2><i class="fas fa-cube"></i> Créer un Modèle 3D</h2>
                
                <form id="modelForm">
                    <div class="form-group">
                        <label for="prompt">Description du modèle :</label>
                        <textarea id="prompt" placeholder="Ex: Un château médiéval avec des tours et des remparts..." rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="type">Type de modèle :</label>
                        <select id="type">
                            <option value="object">Objet</option>
                            <option value="character">Personnage</option>
                            <option value="building">Bâtiment</option>
                            <option value="vehicle">Véhicule</option>
                            <option value="nature">Nature</option>
                            <option value="scene">Scène complète</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="format">Format d'export :</label>
                        <select id="format">
                            <option value="OBJ">OBJ (Standard)</option>
                            <option value="FBX">FBX (Animation)</option>
                            <option value="GLTF">GLTF (Web)</option>
                            <option value="STL">STL (Impression 3D)</option>
                            <option value="PLY">PLY (Recherche)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="quality">Qualité :</label>
                        <select id="quality">
                            <option value="low">Basse (Rapide)</option>
                            <option value="medium">Moyenne (Équilibrée)</option>
                            <option value="high">Haute (Détaillée)</option>
                            <option value="ultra">Ultra (Maximum)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="animated">
                            <label for="animated">Modèle animé</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="textured" checked>
                            <label for="textured">Textures PBR</label>
                        </div>
                    </div>

                    <button type="submit" class="generate-btn" id="generateBtn">
                        <i class="fas fa-magic"></i> Générer le Modèle 3D
                    </button>
                </form>
            </div>

            <div class="preview-panel">
                <h2><i class="fas fa-eye"></i> Aperçu 3D</h2>
                
                <div class="model-viewer" id="modelViewer">
                    <div class="model-placeholder" id="modelPlaceholder">
                        <div class="rotating-cube">
                            <div class="cube-face front"></div>
                            <div class="cube-face back"></div>
                            <div class="cube-face right"></div>
                            <div class="cube-face left"></div>
                            <div class="cube-face top"></div>
                            <div class="cube-face bottom"></div>
                        </div>
                        <p>Aperçu du modèle 3D</p>
                    </div>
                </div>

                <div class="model-controls">
                    <button class="control-btn" id="rotateBtn"><i class="fas fa-sync-alt"></i> Rotation</button>
                    <button class="control-btn" id="zoomBtn"><i class="fas fa-search-plus"></i> Zoom</button>
                    <button class="control-btn" id="wireframeBtn"><i class="fas fa-project-diagram"></i> Wireframe</button>
                    <button class="control-btn" id="lightBtn"><i class="fas fa-lightbulb"></i> Éclairage</button>
                </div>

                <div class="progress-container" id="progressContainer" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Génération en cours...</div>
                </div>

                <div class="model-info" id="modelInfo">
                    <div class="info-item">
                        <div class="label">Vertices</div>
                        <div class="value" id="verticesInfo">--</div>
                    </div>
                    <div class="info-item">
                        <div class="label">Faces</div>
                        <div class="value" id="facesInfo">--</div>
                    </div>
                    <div class="info-item">
                        <div class="label">Taille</div>
                        <div class="value" id="sizeInfo">-- MB</div>
                    </div>
                    <div class="info-item">
                        <div class="label">Format</div>
                        <div class="value" id="formatInfo">--</div>
                    </div>
                </div>

                <div class="download-options" id="downloadOptions" style="display: none;">
                    <button class="download-btn" id="downloadModel">
                        <i class="fas fa-download"></i> Télécharger Modèle
                    </button>
                    <button class="download-btn" id="downloadTextures">
                        <i class="fas fa-image"></i> Télécharger Textures
                    </button>
                </div>
            </div>
        </div>

        <div class="model-gallery">
            <h2><i class="fas fa-images"></i> Galerie des Modèles</h2>
            <div class="gallery-grid" id="galleryGrid">
                <!-- Galerie générée dynamiquement -->
            </div>
        </div>
    </div>

    <script>
        let currentModel = null;
        let isGenerating = false;

        // Générer un modèle 3D
        async function generateModel(formData) {
            isGenerating = true;
            document.getElementById('generateBtn').disabled = true;
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('downloadOptions').style.display = 'none';
            
            try {
                const response = await fetch('/api/generation/3d', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                
                if (result.success) {
                    currentModel = result.model;
                    simulateGeneration();
                } else {
                    console.error('Erreur:', result.error);
                    resetGeneration();
                }
            } catch (error) {
                console.error('Erreur:', error);
                resetGeneration();
            }
        }

        // Simuler la génération
        function simulateGeneration() {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            let progress = 0;
            
            const stages = [
                'Analyse du prompt...',
                'Génération de la géométrie...',
                'Création des textures...',
                'Optimisation du maillage...',
                'Finalisation du modèle...'
            ];
            
            const interval = setInterval(() => {
                progress += 2;
                progressFill.style.width = progress + '%';
                
                const stageIndex = Math.floor((progress / 100) * stages.length);
                if (stageIndex < stages.length) {
                    progressText.textContent = stages[stageIndex];
                }
                
                if (progress >= 100) {
                    clearInterval(interval);
                    completeGeneration();
                }
            }, 100);
        }

        // Compléter la génération
        function completeGeneration() {
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('downloadOptions').style.display = 'flex';
            document.getElementById('generateBtn').disabled = false;
            
            // Mettre à jour les informations du modèle
            updateModelInfo(currentModel);
            
            // Ajouter à la galerie
            addToGallery(currentModel);
            
            isGenerating = false;
        }

        // Réinitialiser la génération
        function resetGeneration() {
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('generateBtn').disabled = false;
            isGenerating = false;
        }

        // Mettre à jour les informations du modèle
        function updateModelInfo(model) {
            document.getElementById('verticesInfo').textContent = model.metadata.vertices.toLocaleString();
            document.getElementById('facesInfo').textContent = model.metadata.faces.toLocaleString();
            document.getElementById('sizeInfo').textContent = model.metadata.fileSize;
            document.getElementById('formatInfo').textContent = model.format;
        }

        // Ajouter à la galerie
        function addToGallery(model) {
            const galleryGrid = document.getElementById('galleryGrid');
            
            const galleryItem = document.createElement('div');
            galleryItem.className = 'gallery-item';
            galleryItem.innerHTML = `
                <div class="gallery-preview">
                    <i class="fas fa-cube"></i>
                </div>
                <div class="gallery-info">
                    <h4>${model.prompt.substring(0, 30)}...</h4>
                    <p>${model.type} • ${model.format}</p>
                </div>
            `;
            
            galleryItem.addEventListener('click', () => {
                currentModel = model;
                updateModelInfo(model);
                document.getElementById('downloadOptions').style.display = 'flex';
            });
            
            galleryGrid.insertBefore(galleryItem, galleryGrid.firstChild);
        }

        // Télécharger le modèle
        function downloadModel() {
            if (currentModel) {
                const link = document.createElement('a');
                link.href = currentModel.url;
                link.download = `louna_3d_${currentModel.id}.${currentModel.format.toLowerCase()}`;
                link.click();
            }
        }

        // Gestionnaires d'événements
        document.getElementById('modelForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            if (isGenerating) return;
            
            const formData = {
                prompt: document.getElementById('prompt').value,
                type: document.getElementById('type').value,
                format: document.getElementById('format').value,
                quality: document.getElementById('quality').value,
                animated: document.getElementById('animated').checked,
                textured: document.getElementById('textured').checked
            };
            
            await generateModel(formData);
        });

        document.getElementById('downloadModel').addEventListener('click', downloadModel);
        
        document.getElementById('downloadTextures').addEventListener('click', () => {
            if (currentModel) {
                const link = document.createElement('a');
                link.href = `/generated/3d/${currentModel.id}_textures.zip`;
                link.download = `louna_3d_textures_${currentModel.id}.zip`;
                link.click();
            }
        });

        // Contrôles de visualisation
        document.getElementById('rotateBtn').addEventListener('click', () => {
            console.log('Rotation activée');
        });

        document.getElementById('zoomBtn').addEventListener('click', () => {
            console.log('Zoom activé');
        });

        document.getElementById('wireframeBtn').addEventListener('click', () => {
            console.log('Mode wireframe');
        });

        document.getElementById('lightBtn').addEventListener('click', () => {
            console.log('Éclairage modifié');
        });
    </script>
</body>
</html>
